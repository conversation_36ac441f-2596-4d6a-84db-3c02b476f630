package com.zxy.product.course.web.controller;

import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.api.DigitalIntelligenceMentorService;
import com.zxy.product.course.entity.DigitalIntelligenceMentor;
import com.zxy.product.system.entity.Member;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * 数智导师对话记录控制器
 */
@Controller
@RequestMapping("/digital-intelligence-mentor")
public class DigitalIntelligenceMentorController {
    
    private static final Logger logger = LoggerFactory.getLogger(DigitalIntelligenceMentorController.class);

    private DigitalIntelligenceMentorService digitalIntelligenceMentorService;

    @Value("${digital.intelligence.mentor.conversation.limit:30}")
    private Integer conversationLimit;

    @Autowired
    public void setDigitalIntelligenceMentorService(DigitalIntelligenceMentorService digitalIntelligenceMentorService) {
        this.digitalIntelligenceMentorService = digitalIntelligenceMentorService;
    }

    /**
     * 根据用户ID查询最新的大模型对话记录
     * @param requestContext 请求上下文
     * @param subject 当前用户
     * @return 对话记录列表，按order字段降序排列
     */
    @JSON("id,memberId,userQuery,botResponse,order,createTime")
    @RequestMapping(value = "/latest-records", method = RequestMethod.GET)
    @Param(name = "memberId", type = String.class, required = true)
    @Permitted
    public List<DigitalIntelligenceMentor> getLatestRecords(RequestContext requestContext, Subject<Member> subject) {
        String memberId = requestContext.getString("memberId");

        logger.info("查询用户{}的最新{}条大模型对话记录", memberId, conversationLimit);

        List<DigitalIntelligenceMentor> records = digitalIntelligenceMentorService.findLatestConversationsByMemberId(memberId, conversationLimit);

        logger.info("查询到{}条对话记录", records.size());

        return records;
    }

    /**
     * 提交问题给大模型并获取回答
     * @param requestContext 请求上下文
     * @param subject 当前用户
     * @return 创建的对话记录
     */
    @JSON("id,memberId,userQuery,botResponse,order,createTime")
    @RequestMapping(value = "/ask-model", method = RequestMethod.POST)
    @Param(name = "memberId", type = String.class, required = true)
    @Param(name = "userQuery", type = String.class, required = true)
    @Permitted
    public DigitalIntelligenceMentor askModel(RequestContext requestContext, Subject<Member> subject) {
        String memberId = requestContext.getString("memberId");
        String userQuery = requestContext.getString("userQuery");

        logger.info("接收到用户{}的问题提交请求", memberId);

        // 调用服务层处理核心逻辑
        return digitalIntelligenceMentorService.askModel(memberId, userQuery);
    }
}

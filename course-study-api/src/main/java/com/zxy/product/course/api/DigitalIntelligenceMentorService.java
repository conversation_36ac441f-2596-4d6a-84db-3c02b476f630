package com.zxy.product.course.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.course.entity.DigitalIntelligenceMentor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 数智导师对话记录服务接口
 */
@RemoteService(timeout = 30000)
public interface DigitalIntelligenceMentorService {

    /**
     * 根据用户ID查询最新的对话记录
     * @param memberId 用户ID
     * @param limit 查询数量限制
     * @return 对话记录列表，按order字段降序排列
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<DigitalIntelligenceMentor> findLatestConversationsByMemberId(String memberId, Integer limit);

    /**
     * 插入对话记录
     * @param digitalIntelligenceMentor 对话记录
     * @return 插入的对话记录
     */
    @Transactional
    DigitalIntelligenceMentor insert(DigitalIntelligenceMentor digitalIntelligenceMentor);

    /**
     * 根据ID获取对话记录
     * @param id 对话记录ID
     * @return 对话记录
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    DigitalIntelligenceMentor get(String id);

    /**
     * 根据用户ID删除对话记录
     * @param memberId 用户ID
     */
    @Transactional
    void deleteByMemberId(String memberId);

    /**
     * 更新对话记录的大模型响应
     * @param id 记录ID
     * @param botResponse 大模型响应内容
     */
    @Transactional
    void updateBotResponse(String id, String botResponse);

    /**
     * 提交问题给大模型并获取回答
     * @param memberId 用户ID
     * @param userQuery 用户问题
     * @return 创建的对话记录
     */
    @Transactional
    DigitalIntelligenceMentor askModel(String memberId, String userQuery);
}

/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.DigitalIntelligenceMentor;
import com.zxy.product.course.jooq.tables.interfaces.IDigitalIntelligenceMentor;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 数智导师结果表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.12.4"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DigitalIntelligenceMentorRecord extends UpdatableRecordImpl<DigitalIntelligenceMentorRecord> implements Record6<String, String, String, String, Integer, Long>, IDigitalIntelligenceMentor {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_digital_intelligence_mentor.f_id</code>. 主键ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_digital_intelligence_mentor.f_id</code>. 主键ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_digital_intelligence_mentor.f_member_id</code>. 用户ID，关联t_member表
     */
    @Override
    public void setMemberId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_digital_intelligence_mentor.f_member_id</code>. 用户ID，关联t_member表
     */
    @Override
    public String getMemberId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_digital_intelligence_mentor.f_user_query</code>. 用户问题
     */
    @Override
    public void setUserQuery(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_digital_intelligence_mentor.f_user_query</code>. 用户问题
     */
    @Override
    public String getUserQuery() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_digital_intelligence_mentor.f_bot_response</code>. 大模型响应
     */
    @Override
    public void setBotResponse(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_digital_intelligence_mentor.f_bot_response</code>. 大模型响应
     */
    @Override
    public String getBotResponse() {
        return (String) get(3);
    }

    /**
     * Setter for <code>course-study.t_digital_intelligence_mentor.f_order</code>. 对话顺序，用于控制显示顺序
     */
    @Override
    public void setOrder(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_digital_intelligence_mentor.f_order</code>. 对话顺序，用于控制显示顺序
     */
    @Override
    public Integer getOrder() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>course-study.t_digital_intelligence_mentor.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_digital_intelligence_mentor.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row6<String, String, String, String, Integer, Long> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    @Override
    public Row6<String, String, String, String, Integer, Long> valuesRow() {
        return (Row6) super.valuesRow();
    }

    @Override
    public Field<String> field1() {
        return DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.ID;
    }

    @Override
    public Field<String> field2() {
        return DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.MEMBER_ID;
    }

    @Override
    public Field<String> field3() {
        return DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.USER_QUERY;
    }

    @Override
    public Field<String> field4() {
        return DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.BOT_RESPONSE;
    }

    @Override
    public Field<Integer> field5() {
        return DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.ORDER;
    }

    @Override
    public Field<Long> field6() {
        return DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.CREATE_TIME;
    }

    @Override
    public String value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getMemberId();
    }

    @Override
    public String value3() {
        return getUserQuery();
    }

    @Override
    public String value4() {
        return getBotResponse();
    }

    @Override
    public Integer value5() {
        return getOrder();
    }

    @Override
    public Long value6() {
        return getCreateTime();
    }

    @Override
    public DigitalIntelligenceMentorRecord value1(String value) {
        setId(value);
        return this;
    }

    @Override
    public DigitalIntelligenceMentorRecord value2(String value) {
        setMemberId(value);
        return this;
    }

    @Override
    public DigitalIntelligenceMentorRecord value3(String value) {
        setUserQuery(value);
        return this;
    }

    @Override
    public DigitalIntelligenceMentorRecord value4(String value) {
        setBotResponse(value);
        return this;
    }

    @Override
    public DigitalIntelligenceMentorRecord value5(Integer value) {
        setOrder(value);
        return this;
    }

    @Override
    public DigitalIntelligenceMentorRecord value6(Long value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public DigitalIntelligenceMentorRecord values(String value1, String value2, String value3, String value4, Integer value5, Long value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    @Override
    public void from(IDigitalIntelligenceMentor from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setUserQuery(from.getUserQuery());
        setBotResponse(from.getBotResponse());
        setOrder(from.getOrder());
        setCreateTime(from.getCreateTime());
    }

    @Override
    public <E extends IDigitalIntelligenceMentor> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached DigitalIntelligenceMentorRecord
     */
    public DigitalIntelligenceMentorRecord() {
        super(DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR);
    }

    /**
     * Create a detached, initialised DigitalIntelligenceMentorRecord
     */
    public DigitalIntelligenceMentorRecord(String id, String memberId, String userQuery, String botResponse, Integer order, Long createTime) {
        super(DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR);

        set(0, id);
        set(1, memberId);
        set(2, userQuery);
        set(3, botResponse);
        set(4, order);
        set(5, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.DigitalIntelligenceMentorEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.DigitalIntelligenceMentorEntity pojo = (com.zxy.product.course.jooq.tables.pojos.DigitalIntelligenceMentorEntity)source;
        pojo.into(this);
        return true;
    }
}

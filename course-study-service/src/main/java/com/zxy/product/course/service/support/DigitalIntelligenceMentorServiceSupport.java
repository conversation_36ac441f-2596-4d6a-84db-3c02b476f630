package com.zxy.product.course.service.support;

import com.alibaba.fastjson.JSON;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.api.DigitalIntelligenceMentorService;
import com.zxy.product.course.entity.DigitalIntelligenceMentor;
import com.zxy.product.course.service.dto.ModelRequestDto;
import com.zxy.product.course.service.dto.ModelResponseDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

import static com.zxy.product.course.jooq.tables.DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR;

/**
 * 数智导师对话记录服务实现类
 */
@Service
public class DigitalIntelligenceMentorServiceSupport implements DigitalIntelligenceMentorService {

    private static final Logger logger = LoggerFactory.getLogger(DigitalIntelligenceMentorServiceSupport.class);

    private CommonDao<DigitalIntelligenceMentor> digitalIntelligenceMentorDao;
    private RestTemplate restTemplate;

    @Value("${digital.intelligence.mentor.model.url}")
    private String modelUrl;

    @Autowired
    public void setDigitalIntelligenceMentorDao(CommonDao<DigitalIntelligenceMentor> digitalIntelligenceMentorDao) {
        this.digitalIntelligenceMentorDao = digitalIntelligenceMentorDao;
    }

    @Autowired
    public void setRestTemplate(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Override
    public List<DigitalIntelligenceMentor> findLatestConversationsByMemberId(String memberId, Integer limit) {
        return digitalIntelligenceMentorDao.execute(context ->
            context.select()
                .from(DIGITAL_INTELLIGENCE_MENTOR)
                .where(DIGITAL_INTELLIGENCE_MENTOR.MEMBER_ID.eq(memberId))
                .orderBy(DIGITAL_INTELLIGENCE_MENTOR.ORDER.desc())
                .limit(limit)
                .fetch(r -> r.into(DigitalIntelligenceMentor.class))
        );
    }

    @Override
    public DigitalIntelligenceMentor insert(DigitalIntelligenceMentor digitalIntelligenceMentor) {
        digitalIntelligenceMentorDao.insert(digitalIntelligenceMentor);
        return digitalIntelligenceMentor;
    }

    @Override
    public DigitalIntelligenceMentor get(String id) {
        return digitalIntelligenceMentorDao.get(id);
    }

    @Override
    public void deleteByMemberId(String memberId) {
        digitalIntelligenceMentorDao.delete(DIGITAL_INTELLIGENCE_MENTOR.MEMBER_ID.eq(memberId));
    }

    @Override
    public void updateBotResponse(String id, String botResponse) {
        digitalIntelligenceMentorDao.execute(context ->
            context.update(DIGITAL_INTELLIGENCE_MENTOR)
                .set(DIGITAL_INTELLIGENCE_MENTOR.BOT_RESPONSE, botResponse)
                .where(DIGITAL_INTELLIGENCE_MENTOR.ID.eq(id))
                .execute()
        );
    }

    @Override
    public DigitalIntelligenceMentor askModel(String memberId, String userQuery) {
        logger.info("用户{}提交问题给大模型: {}", memberId, userQuery);

        // 1. 先写入数据库，生成记录ID作为msgId
        DigitalIntelligenceMentor record = new DigitalIntelligenceMentor();
        record.forInsert(); // 生成ID和创建时间
        record.setMemberId(memberId);
        record.setUserQuery(userQuery);
        record.setOrder(1); // 可以根据业务需要设置order值

        // 插入数据库
        this.insert(record);
        String msgId = record.getId();

        logger.info("已创建对话记录，ID: {}", msgId);

        try {
            // 2. 调用大模型接口
            ModelRequestDto modelRequest = new ModelRequestDto(msgId, userQuery);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<ModelRequestDto> requestEntity = new HttpEntity<>(modelRequest, headers);

            logger.info("调用大模型接口: {}, 请求参数: {}", modelUrl, modelRequest);

            ResponseEntity<String> response = restTemplate.postForEntity(modelUrl, requestEntity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                logger.info("大模型响应: {}", responseBody);

                // 3. 解析响应
                ModelResponseDto modelResponse = JSON.parseObject(responseBody, ModelResponseDto.class);

                if ("0".equals(modelResponse.getCode()) && msgId.equals(modelResponse.getMsgId())) {
                    // 4. 更新数据库中的bot_response字段
                    this.updateBotResponse(msgId, modelResponse.getAnswer());
                    record.setBotResponse(modelResponse.getAnswer());

                    logger.info("已更新对话记录的大模型响应，ID: {}", msgId);
                } else {
                    logger.error("大模型响应异常，code: {}, msgId: {}, 期望msgId: {}",
                        modelResponse.getCode(), modelResponse.getMsgId(), msgId);
                }
            } else {
                logger.error("调用大模型接口失败，状态码: {}", response.getStatusCode());
            }

        } catch (Exception e) {
            logger.error("调用大模型接口异常，msgId: {}", msgId, e);
        }

        return record;
    }
}
